import React, { useRef, useState } from 'react';
import { usePDFGenerator } from '../hooks/usePDFGenerator';
import { PDFTemplate } from './PDFTemplate';
import { Transaction } from '../lib/transactionService';

export function PDFDemo() {
  const pdfTemplateRef = useRef<HTMLDivElement>(null);
  const { generatePDF, previewPDF, isGenerating, error, clearError } = usePDFGenerator();
  const [showDemo, setShowDemo] = useState(false);

  // Sample transaction data for demo
  const sampleTransactions: Transaction[] = [
    {
      id: '1',
      title: 'Salary Payment',
      description: 'Monthly salary from ABC Company',
      amount: 5000,
      type: 'income',
      category: 'Salary',
      date: '2024-01-15',
      userId: 'demo-user'
    },
    {
      id: '2',
      title: 'Grocery Shopping',
      description: 'Weekly groceries at SuperMart',
      amount: -150.75,
      type: 'expense',
      category: 'Food',
      date: '2024-01-16',
      userId: 'demo-user'
    },
    {
      id: '3',
      title: 'Freelance Project',
      description: 'Web development project payment',
      amount: 1200,
      type: 'income',
      category: 'Freelance',
      date: '2024-01-18',
      userId: 'demo-user'
    },
    {
      id: '4',
      title: 'Electric Bill',
      description: 'Monthly electricity bill',
      amount: -89.50,
      type: 'expense',
      category: 'Utilities',
      date: '2024-01-20',
      userId: 'demo-user'
    },
    {
      id: '5',
      title: 'Investment Dividend',
      description: 'Quarterly dividend payment',
      amount: 300,
      type: 'income',
      category: 'Investment',
      date: '2024-01-22',
      userId: 'demo-user'
    }
  ];

  const handleGeneratePDF = async () => {
    if (pdfTemplateRef.current) {
      clearError();
      const success = await generatePDF(pdfTemplateRef.current, {
        filename: `demo-transaction-report-${new Date().toISOString().split('T')[0]}.pdf`,
        margin: 0.5,
        jsPDF: { 
          unit: 'in', 
          format: 'a4', 
          orientation: 'landscape' 
        }
      });
      
      if (!success && error) {
        alert(`Failed to generate PDF: ${error}`);
      }
    }
  };

  const handlePreviewPDF = async () => {
    if (pdfTemplateRef.current) {
      clearError();
      const success = await previewPDF(pdfTemplateRef.current, {
        margin: 0.5,
        jsPDF: { 
          unit: 'in', 
          format: 'a4', 
          orientation: 'landscape' 
        }
      });
      
      if (!success && error) {
        alert(`Failed to preview PDF: ${error}`);
      }
    }
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-2xl font-semibold mb-4">📄 HTML2PDF.js Demo</h2>
      
      <div className="mb-6">
        <p className="text-gray-600 mb-4">
          This demo shows how to use html2pdf.js to generate PDF reports from React components.
          Click the buttons below to test PDF generation and preview functionality.
        </p>
        
        {/* Error Display */}
        {error && (
          <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
            <div className="flex">
              <div className="flex-shrink-0">
                <span className="text-red-400">⚠️</span>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">PDF Generation Error</h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>{error}</p>
                </div>
                <div className="mt-4">
                  <button
                    onClick={clearError}
                    className="text-sm bg-red-100 text-red-800 px-3 py-1 rounded-md hover:bg-red-200"
                  >
                    Dismiss
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Demo Controls */}
        <div className="flex flex-wrap gap-3 mb-4">
          <button
            onClick={handleGeneratePDF}
            disabled={isGenerating}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isGenerating ? '⏳ Generating...' : '📄 Download PDF'}
          </button>
          
          <button
            onClick={handlePreviewPDF}
            disabled={isGenerating}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            👁️ Preview PDF
          </button>
          
          <button
            onClick={() => setShowDemo(!showDemo)}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {showDemo ? '🙈 Hide Template' : '👀 Show Template'}
          </button>
        </div>

        {/* Sample Data Info */}
        <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
          <h3 className="text-sm font-medium text-blue-800 mb-2">Sample Data</h3>
          <p className="text-sm text-blue-700">
            This demo uses {sampleTransactions.length} sample transactions including income and expenses.
            The PDF will be generated in landscape A4 format with a professional layout.
          </p>
        </div>
      </div>

      {/* Template Preview */}
      {showDemo && (
        <div className="mb-6 border border-gray-300 rounded-lg p-4 bg-gray-50">
          <h3 className="text-lg font-medium mb-3">Template Preview</h3>
          <div className="bg-white border rounded p-4 max-h-96 overflow-y-auto">
            <PDFTemplate 
              transactions={sampleTransactions} 
              title="Demo Transaction Report"
              companyName="HTML2PDF.js Demo"
            />
          </div>
        </div>
      )}

      {/* Hidden PDF Template for html2pdf.js */}
      <div 
        ref={pdfTemplateRef}
        style={{ 
          position: 'absolute', 
          left: '-9999px', 
          top: '-9999px',
          width: '1200px',
          backgroundColor: 'white'
        }}
      >
        <PDFTemplate 
          transactions={sampleTransactions} 
          title="Demo Transaction Report"
          companyName="HTML2PDF.js Demo"
        />
      </div>

      {/* Features List */}
      <div className="mt-6">
        <h3 className="text-lg font-medium mb-3">✨ Features Demonstrated</h3>
        <ul className="list-disc pl-5 space-y-1 text-sm text-gray-600">
          <li>PDF generation from React components using html2pdf.js</li>
          <li>Professional PDF template with styled tables and summaries</li>
          <li>PDF preview functionality (opens in new window)</li>
          <li>Error handling and loading states</li>
          <li>Responsive design that works well in PDF format</li>
          <li>Custom PDF options (format, orientation, margins)</li>
        </ul>
      </div>
    </div>
  );
}
