import React from 'react';
import { Transaction } from '../lib/transactionService';

interface PDFTemplateProps {
  transactions: Transaction[];
  title?: string;
  companyName?: string;
}

export const PDFTemplate: React.FC<PDFTemplateProps> = ({ 
  transactions, 
  title = "Transaction Report",
  companyName = "Electron Finance App"
}) => {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Calculate totals
  const totals = transactions.reduce((acc, transaction) => {
    const amount = Math.abs(transaction.amount);
    if (transaction.type === 'income') {
      acc.income += amount;
    } else if (transaction.type === 'expense') {
      acc.expenses += amount;
    }
    return acc;
  }, { income: 0, expenses: 0 });

  const balance = totals.income - totals.expenses;

  return (
    <div style={{ 
      fontFamily: 'Arial, sans-serif', 
      padding: '20px', 
      backgroundColor: 'white',
      color: 'black',
      fontSize: '12px',
      lineHeight: '1.4'
    }}>
      {/* Header */}
      <div style={{ textAlign: 'center', marginBottom: '30px', borderBottom: '2px solid #333', paddingBottom: '15px' }}>
        <h1 style={{ margin: '0 0 10px 0', fontSize: '24px', fontWeight: 'bold' }}>{companyName}</h1>
        <h2 style={{ margin: '0 0 10px 0', fontSize: '18px', color: '#666' }}>{title}</h2>
        <p style={{ margin: '0', color: '#888' }}>Generated on {new Date().toLocaleDateString()}</p>
      </div>

      {/* Summary Cards */}
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(4, 1fr)', 
        gap: '15px', 
        marginBottom: '30px' 
      }}>
        <div style={{ 
          border: '1px solid #22c55e', 
          borderRadius: '8px', 
          padding: '15px', 
          backgroundColor: '#f0fdf4' 
        }}>
          <h3 style={{ margin: '0 0 8px 0', fontSize: '12px', color: '#166534', fontWeight: '600' }}>
            Total Income
          </h3>
          <p style={{ margin: '0', fontSize: '18px', fontWeight: 'bold', color: '#15803d' }}>
            {formatCurrency(totals.income)}
          </p>
        </div>
        
        <div style={{ 
          border: '1px solid #ef4444', 
          borderRadius: '8px', 
          padding: '15px', 
          backgroundColor: '#fef2f2' 
        }}>
          <h3 style={{ margin: '0 0 8px 0', fontSize: '12px', color: '#991b1b', fontWeight: '600' }}>
            Total Expenses
          </h3>
          <p style={{ margin: '0', fontSize: '18px', fontWeight: 'bold', color: '#dc2626' }}>
            {formatCurrency(totals.expenses)}
          </p>
        </div>
        
        <div style={{ 
          border: `1px solid ${balance >= 0 ? '#3b82f6' : '#ef4444'}`, 
          borderRadius: '8px', 
          padding: '15px', 
          backgroundColor: balance >= 0 ? '#eff6ff' : '#fef2f2' 
        }}>
          <h3 style={{ 
            margin: '0 0 8px 0', 
            fontSize: '12px', 
            color: balance >= 0 ? '#1e40af' : '#991b1b', 
            fontWeight: '600' 
          }}>
            Balance
          </h3>
          <p style={{ 
            margin: '0', 
            fontSize: '18px', 
            fontWeight: 'bold', 
            color: balance >= 0 ? '#2563eb' : '#dc2626' 
          }}>
            {formatCurrency(balance)}
          </p>
        </div>
        
        <div style={{ 
          border: '1px solid #6b7280', 
          borderRadius: '8px', 
          padding: '15px', 
          backgroundColor: '#f9fafb' 
        }}>
          <h3 style={{ margin: '0 0 8px 0', fontSize: '12px', color: '#374151', fontWeight: '600' }}>
            Transactions
          </h3>
          <p style={{ margin: '0', fontSize: '18px', fontWeight: 'bold', color: '#111827' }}>
            {transactions.length}
          </p>
        </div>
      </div>

      {/* Transactions Table */}
      <table style={{ 
        width: '100%', 
        borderCollapse: 'collapse', 
        border: '1px solid #d1d5db',
        marginBottom: '20px'
      }}>
        <thead>
          <tr style={{ backgroundColor: '#f9fafb' }}>
            <th style={{ 
              border: '1px solid #d1d5db', 
              padding: '12px 8px', 
              textAlign: 'left', 
              fontSize: '11px', 
              fontWeight: '600', 
              color: '#6b7280',
              textTransform: 'uppercase'
            }}>
              Date
            </th>
            <th style={{ 
              border: '1px solid #d1d5db', 
              padding: '12px 8px', 
              textAlign: 'left', 
              fontSize: '11px', 
              fontWeight: '600', 
              color: '#6b7280',
              textTransform: 'uppercase'
            }}>
              Title
            </th>
            <th style={{ 
              border: '1px solid #d1d5db', 
              padding: '12px 8px', 
              textAlign: 'left', 
              fontSize: '11px', 
              fontWeight: '600', 
              color: '#6b7280',
              textTransform: 'uppercase'
            }}>
              Category
            </th>
            <th style={{ 
              border: '1px solid #d1d5db', 
              padding: '12px 8px', 
              textAlign: 'left', 
              fontSize: '11px', 
              fontWeight: '600', 
              color: '#6b7280',
              textTransform: 'uppercase'
            }}>
              Type
            </th>
            <th style={{ 
              border: '1px solid #d1d5db', 
              padding: '12px 8px', 
              textAlign: 'right', 
              fontSize: '11px', 
              fontWeight: '600', 
              color: '#6b7280',
              textTransform: 'uppercase'
            }}>
              Amount
            </th>
          </tr>
        </thead>
        <tbody>
          {transactions.map((transaction, index) => (
            <tr key={transaction.id} style={{ 
              backgroundColor: index % 2 === 0 ? 'white' : '#f9fafb' 
            }}>
              <td style={{ 
                border: '1px solid #d1d5db', 
                padding: '10px 8px', 
                fontSize: '11px', 
                color: '#111827' 
              }}>
                {formatDate(transaction.date)}
              </td>
              <td style={{ border: '1px solid #d1d5db', padding: '10px 8px' }}>
                <div style={{ fontSize: '11px', fontWeight: '500', color: '#111827', marginBottom: '2px' }}>
                  {transaction.title}
                </div>
                {transaction.description && (
                  <div style={{ fontSize: '10px', color: '#6b7280' }}>
                    {transaction.description.length > 50 
                      ? transaction.description.substring(0, 50) + '...' 
                      : transaction.description}
                  </div>
                )}
              </td>
              <td style={{ 
                border: '1px solid #d1d5db', 
                padding: '10px 8px', 
                fontSize: '11px', 
                color: '#111827' 
              }}>
                {transaction.category || 'Uncategorized'}
              </td>
              <td style={{ border: '1px solid #d1d5db', padding: '10px 8px' }}>
                <span style={{
                  display: 'inline-block',
                  padding: '4px 8px',
                  fontSize: '10px',
                  fontWeight: '600',
                  borderRadius: '4px',
                  backgroundColor: transaction.type === 'income' 
                    ? '#dcfce7' 
                    : transaction.type === 'expense' 
                    ? '#fee2e2' 
                    : '#dbeafe',
                  color: transaction.type === 'income' 
                    ? '#166534' 
                    : transaction.type === 'expense' 
                    ? '#991b1b' 
                    : '#1e40af'
                }}>
                  {transaction.type}
                </span>
              </td>
              <td style={{ 
                border: '1px solid #d1d5db', 
                padding: '10px 8px', 
                textAlign: 'right', 
                fontSize: '11px', 
                fontWeight: '500',
                color: transaction.type === 'income' 
                  ? '#059669' 
                  : transaction.type === 'expense' 
                  ? '#dc2626' 
                  : '#2563eb'
              }}>
                {transaction.type === 'expense' ? '-' : '+'}
                {formatCurrency(Math.abs(transaction.amount))}
              </td>
            </tr>
          ))}
        </tbody>
        <tfoot>
          <tr style={{ backgroundColor: '#f3f4f6', fontWeight: '600' }}>
            <td colSpan={4} style={{ 
              border: '1px solid #d1d5db', 
              padding: '12px 8px', 
              textAlign: 'right',
              fontSize: '12px'
            }}>
              Net Total:
            </td>
            <td style={{ 
              border: '1px solid #d1d5db', 
              padding: '12px 8px', 
              textAlign: 'right',
              fontSize: '12px',
              fontWeight: 'bold',
              color: balance >= 0 ? '#059669' : '#dc2626'
            }}>
              {formatCurrency(balance)}
            </td>
          </tr>
        </tfoot>
      </table>

      {/* Footer */}
      <div style={{ 
        marginTop: '30px', 
        paddingTop: '15px', 
        borderTop: '2px solid #d1d5db', 
        textAlign: 'center', 
        fontSize: '10px', 
        color: '#6b7280' 
      }}>
        <p style={{ margin: '0 0 5px 0' }}>Report generated on {new Date().toLocaleString()}</p>
        <p style={{ margin: '0' }}>{companyName} - Transaction Management System</p>
      </div>
    </div>
  );
};
