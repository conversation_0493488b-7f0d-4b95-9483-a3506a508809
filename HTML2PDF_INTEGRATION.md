# HTML2PDF.js Integration

This project now includes html2pdf.js integration for generating PDF reports from React components.

## 📦 What's Included

### 1. Dependencies Added
- `html2pdf.js` - Main library for PDF generation
- Custom TypeScript declarations in `src/renderer/types/html2pdf.d.ts`

### 2. Components Created

#### `PDFTemplate.tsx`
A professional PDF template component that renders transaction data in a clean, printable format:
- Company header with title and generation date
- Summary cards showing totals (income, expenses, balance, transaction count)
- Styled transaction table with alternating row colors
- Footer with generation timestamp
- Optimized for PDF rendering with inline styles

#### `usePDFGenerator.ts` Hook
A custom React hook that provides PDF generation functionality:
- `generatePDF()` - Downloads PDF file directly
- `previewPDF()` - Opens PDF in new window for preview
- `generatePDFBlob()` - Returns PDF as Blob for custom handling
- Loading states and error handling
- Configurable PDF options (format, orientation, margins, etc.)

#### `PDFDemo.tsx`
A demonstration component showing how to use the PDF functionality:
- Sample transaction data
- Interactive buttons for PDF generation and preview
- Template preview toggle
- Error handling demonstration
- Feature documentation

### 3. Enhanced Components

#### `BulkTransactionPrintView.tsx`
Updated with PDF functionality:
- Added "Download PDF" and "Preview PDF" buttons
- Integrated with `usePDFGenerator` hook
- Hidden PDF template for html2pdf.js rendering
- Error display for PDF generation issues
- Loading states during PDF generation

## 🚀 Usage Examples

### Basic PDF Generation
```tsx
import { usePDFGenerator } from '../hooks/usePDFGenerator';
import { PDFTemplate } from '../components/PDFTemplate';

function MyComponent() {
  const { generatePDF, isGenerating, error } = usePDFGenerator();
  const pdfRef = useRef<HTMLDivElement>(null);

  const handleDownloadPDF = async () => {
    if (pdfRef.current) {
      await generatePDF(pdfRef.current, {
        filename: 'my-report.pdf',
        margin: 0.5,
        jsPDF: { 
          unit: 'in', 
          format: 'a4', 
          orientation: 'landscape' 
        }
      });
    }
  };

  return (
    <div>
      <button onClick={handleDownloadPDF} disabled={isGenerating}>
        {isGenerating ? 'Generating...' : 'Download PDF'}
      </button>
      
      {/* Hidden template for PDF generation */}
      <div ref={pdfRef} style={{ position: 'absolute', left: '-9999px' }}>
        <PDFTemplate transactions={myTransactions} />
      </div>
    </div>
  );
}
```

### PDF Preview
```tsx
const { previewPDF } = usePDFGenerator();

const handlePreview = async () => {
  if (pdfRef.current) {
    await previewPDF(pdfRef.current);
  }
};
```

### Custom PDF Options
```tsx
const pdfOptions = {
  margin: [0.5, 0.5, 0.5, 0.5], // top, right, bottom, left
  filename: 'custom-report.pdf',
  image: { type: 'jpeg', quality: 0.98 },
  html2canvas: { 
    scale: 2, 
    useCORS: true,
    backgroundColor: '#ffffff'
  },
  jsPDF: { 
    unit: 'in', 
    format: 'letter', 
    orientation: 'portrait' 
  }
};

await generatePDF(element, pdfOptions);
```

## 🎨 PDF Template Features

The `PDFTemplate` component includes:
- **Professional Layout**: Clean, business-ready design
- **Responsive Tables**: Properly formatted transaction tables
- **Summary Cards**: Visual summary of financial data
- **Inline Styles**: Optimized for PDF rendering (no external CSS dependencies)
- **Customizable**: Easy to modify colors, fonts, and layout
- **Print-Friendly**: Optimized for both screen and PDF output

## 🔧 Configuration Options

### PDF Generation Options
- **Format**: A4, Letter, Legal, etc.
- **Orientation**: Portrait or Landscape
- **Margins**: Customizable margins in inches
- **Quality**: Image quality settings
- **Scale**: HTML2Canvas scale factor for better quality

### Template Customization
The `PDFTemplate` component accepts:
- `transactions`: Array of transaction data
- `title`: Custom report title
- `companyName`: Company/app name for header

## 🧪 Testing

1. **Run the Application**:
   ```bash
   npm run dev
   ```

2. **Navigate to Main Screen**: The PDF demo is integrated into the main screen

3. **Test PDF Features**:
   - Click "Download PDF" to generate and download a PDF
   - Click "Preview PDF" to open PDF in new window
   - Click "Show Template" to see the template preview
   - Test error handling by checking console for any issues

## 📝 Notes

- **Browser Compatibility**: html2pdf.js works in modern browsers
- **Performance**: Large documents may take time to generate
- **Styling**: Use inline styles for best PDF rendering results
- **Images**: Ensure images are loaded before PDF generation
- **CORS**: Enable CORS for external images if needed

## 🔍 Troubleshooting

### Common Issues:
1. **PDF Generation Fails**: Check browser console for errors
2. **Styling Issues**: Ensure styles are inline or properly loaded
3. **Large Files**: Consider reducing image quality or scale
4. **CORS Errors**: Enable CORS for external resources

### Error Handling:
The `usePDFGenerator` hook provides error states and messages for debugging PDF generation issues.
