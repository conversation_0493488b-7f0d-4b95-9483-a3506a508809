declare module 'html2pdf.js' {
  interface Html2PdfOptions {
    margin?: number | [number, number, number, number];
    filename?: string;
    image?: {
      type: string;
      quality: number;
    };
    html2canvas?: {
      scale: number;
      useCORS?: boolean;
      allowTaint?: boolean;
      backgroundColor?: string;
      [key: string]: any;
    };
    jsPDF?: {
      unit: string;
      format: string;
      orientation: string;
      [key: string]: any;
    };
    [key: string]: any;
  }

  interface Html2Pdf {
    set(options: Html2PdfOptions): Html2Pdf;
    from(element: HTMLElement | string): Html2Pdf;
    save(): Promise<void>;
    outputPdf(type: 'blob'): Promise<Blob>;
    outputPdf(type: 'datauristring'): Promise<string>;
    outputPdf(type: 'arraybuffer'): Promise<ArrayBuffer>;
    outputPdf(type: 'pdf'): Promise<any>;
    outputPdf(type?: string): Promise<any>;
  }

  function html2pdf(): Html2Pdf;

  export = html2pdf;
}
