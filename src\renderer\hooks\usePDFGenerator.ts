import { useCallback, useState } from 'react';
import html2pdf from 'html2pdf.js';

export interface PDFOptions {
  margin?: number | [number, number, number, number];
  filename?: string;
  image?: { type: string; quality: number };
  html2canvas?: { scale: number; useCORS: boolean };
  jsPDF?: { unit: string; format: string; orientation: string };
}

export const usePDFGenerator = () => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const generatePDF = useCallback(async (
    element: HTMLElement | string,
    options: PDFOptions = {}
  ) => {
    setIsGenerating(true);
    setError(null);

    try {
      const defaultOptions = {
        margin: 0.5,
        filename: `transaction-report-${new Date().toISOString().split('T')[0]}.pdf`,
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: { 
          scale: 2, 
          useCORS: true,
          allowTaint: true,
          backgroundColor: '#ffffff'
        },
        jsPDF: { 
          unit: 'in', 
          format: 'a4', 
          orientation: 'landscape' 
        }
      };

      const finalOptions = { ...defaultOptions, ...options };

      await html2pdf()
        .set(finalOptions)
        .from(element)
        .save();

      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate PDF';
      setError(errorMessage);
      console.error('PDF generation error:', err);
      return false;
    } finally {
      setIsGenerating(false);
    }
  }, []);

  const generatePDFBlob = useCallback(async (
    element: HTMLElement | string,
    options: PDFOptions = {}
  ): Promise<Blob | null> => {
    setIsGenerating(true);
    setError(null);

    try {
      const defaultOptions = {
        margin: 0.5,
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: { 
          scale: 2, 
          useCORS: true,
          allowTaint: true,
          backgroundColor: '#ffffff'
        },
        jsPDF: { 
          unit: 'in', 
          format: 'a4', 
          orientation: 'landscape' 
        }
      };

      const finalOptions = { ...defaultOptions, ...options };

      const pdf = await html2pdf()
        .set(finalOptions)
        .from(element)
        .outputPdf('blob');

      return pdf;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate PDF';
      setError(errorMessage);
      console.error('PDF generation error:', err);
      return null;
    } finally {
      setIsGenerating(false);
    }
  }, []);

  const previewPDF = useCallback(async (
    element: HTMLElement | string,
    options: PDFOptions = {}
  ) => {
    setIsGenerating(true);
    setError(null);

    try {
      const defaultOptions = {
        margin: 0.5,
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: { 
          scale: 2, 
          useCORS: true,
          allowTaint: true,
          backgroundColor: '#ffffff'
        },
        jsPDF: { 
          unit: 'in', 
          format: 'a4', 
          orientation: 'landscape' 
        }
      };

      const finalOptions = { ...defaultOptions, ...options };

      const pdfDataUri = await html2pdf()
        .set(finalOptions)
        .from(element)
        .outputPdf('datauristring');

      // Open PDF in new window/tab
      const newWindow = window.open();
      if (newWindow) {
        newWindow.document.write(`
          <iframe 
            src="${pdfDataUri}" 
            style="width:100%; height:100%; border:none;"
            title="PDF Preview">
          </iframe>
        `);
      }

      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to preview PDF';
      setError(errorMessage);
      console.error('PDF preview error:', err);
      return false;
    } finally {
      setIsGenerating(false);
    }
  }, []);

  return {
    generatePDF,
    generatePDFBlob,
    previewPDF,
    isGenerating,
    error,
    clearError: () => setError(null)
  };
};
